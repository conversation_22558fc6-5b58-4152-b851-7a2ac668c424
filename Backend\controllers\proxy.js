const ErrorResponse = require('../utils/errorResponse');
const Content = require('../models/Content');
const { getContentFileAccess } = require('../utils/accessControl');
const { getS3Instance, isUsingS3Storage } = require('../utils/storageHelper');
const path = require('path');
const fs = require('fs');

/**
 * Get content type based on file extension
 */
const getContentType = (filePath) => {
  const ext = path.extname(filePath).toLowerCase();
  const contentTypes = {
    '.pdf': 'application/pdf',
    '.mp4': 'video/mp4',
    '.avi': 'video/x-msvideo',
    '.mov': 'video/quicktime',
    '.wmv': 'video/x-ms-wmv',
    '.flv': 'video/x-flv',
    '.webm': 'video/webm',
    '.mkv': 'video/x-matroska',
    '.mp3': 'audio/mpeg',
    '.wav': 'audio/wav',
    '.ogg': 'audio/ogg',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.txt': 'text/plain',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  };
  return contentTypes[ext] || 'application/octet-stream';
};

/**
 * Extract S3 key from URL
 */
const extractS3Key = (url) => {
  if (!url) return null;

  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

    if (urlObj.hostname.includes('.s3.') || urlObj.hostname.includes('.s3-')) {
      return pathParts.join('/');
    } else if (urlObj.hostname.startsWith('s3.')) {
      return pathParts.slice(1).join('/');
    }

    return null;
  } catch (error) {
    console.error('Error extracting S3 key:', error);
    return null;
  }
};

/**
 * Stream file from S3 with range support
 */
const streamFromS3 = async (s3Key, req, res) => {
  const s3 = getS3Instance();
  if (!s3) {
    throw new Error('S3 not configured');
  }

  try {
    // Get object metadata first
    const headParams = {
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: s3Key
    };

    const metadata = await s3.headObject(headParams).promise();
    const fileSize = metadata.ContentLength;
    const contentType = metadata.ContentType || getContentType(s3Key);

    // Handle range requests for video seeking
    const range = req.headers.range;
    if (range) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;

      const getParams = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: s3Key,
        Range: `bytes=${start}-${end}`
      };

      res.status(206);
      res.setHeader('Content-Range', `bytes ${start}-${end}/${fileSize}`);
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Length', chunksize);
      res.setHeader('Content-Type', contentType);

      const stream = s3.getObject(getParams).createReadStream();
      stream.pipe(res);
    } else {
      // Full file request
      const getParams = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: s3Key
      };

      res.setHeader('Content-Length', fileSize);
      res.setHeader('Content-Type', contentType);
      res.setHeader('Accept-Ranges', 'bytes');

      const stream = s3.getObject(getParams).createReadStream();
      stream.pipe(res);
    }
  } catch (error) {
    console.error('Error streaming from S3:', error);
    throw error;
  }
};

/**
 * Stream local file with range support
 */
const streamLocalFile = (filePath, req, res) => {
  const fullPath = path.join(process.cwd(), 'uploads', filePath);

  if (!fs.existsSync(fullPath)) {
    throw new Error('File not found');
  }

  const stat = fs.statSync(fullPath);
  const fileSize = stat.size;
  const contentType = getContentType(filePath);

  const range = req.headers.range;
  if (range) {
    const parts = range.replace(/bytes=/, "").split("-");
    const start = parseInt(parts[0], 10);
    const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
    const chunksize = (end - start) + 1;

    res.status(206);
    res.setHeader('Content-Range', `bytes ${start}-${end}/${fileSize}`);
    res.setHeader('Accept-Ranges', 'bytes');
    res.setHeader('Content-Length', chunksize);
    res.setHeader('Content-Type', contentType);

    const stream = fs.createReadStream(fullPath, { start, end });
    stream.pipe(res);
  } else {
    res.setHeader('Content-Length', fileSize);
    res.setHeader('Content-Type', contentType);
    res.setHeader('Accept-Ranges', 'bytes');

    const stream = fs.createReadStream(fullPath);
    stream.pipe(res);
  }
};

/**
 * @desc    Serve content file through proxy
 * @route   GET /api/proxy/content/:contentId
 * @access  Private (requires authentication and purchase)
 */
exports.serveContent = async (req, res, next) => {
  try {
    const { contentId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Get content
    const content = await Content.findById(contentId);
    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    // Check access permissions - allow both full and preview access
    const fileAccess = await getContentFileAccess(content, userId, userRole);
    if (!fileAccess.canAccessFull && !fileAccess.canAccessPreview) {
      return next(new ErrorResponse('Access denied. Authentication required to access content.', 403));
    }

    // For preview access, use preview URL if available, otherwise use full URL
    let serveFileUrl = content.fileUrl;
    if (!fileAccess.canAccessFull && fileAccess.canAccessPreview && content.previewUrl) {
      serveFileUrl = content.previewUrl;
    }

    if (!serveFileUrl) {
      return next(new ErrorResponse('Content file not available', 404));
    }

    // Set security headers
    res.setHeader('Cache-Control', 'private, no-cache');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');

    if (isUsingS3Storage() && serveFileUrl.includes('s3')) {
      // Stream from S3
      const s3Key = extractS3Key(serveFileUrl);
      if (!s3Key) {
        return next(new ErrorResponse('Invalid file URL', 400));
      }
      await streamFromS3(s3Key, req, res);
    } else {
      // Stream local file
      streamLocalFile(serveFileUrl, req, res);
    }

  } catch (error) {
    console.error('Error serving content:', error);
    next(new ErrorResponse('Error serving content', 500));
  }
};

/**
 * @desc    Serve preview file through proxy
 * @route   GET /api/proxy/preview/:contentId
 * @access  Private (requires authentication)
 */
exports.servePreview = async (req, res, next) => {
  try {
    const { contentId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Get content
    const content = await Content.findById(contentId);
    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    // Check access permissions
    const fileAccess = await getContentFileAccess(content, userId, userRole);
    if (!fileAccess.canAccessPreview && !fileAccess.canAccessFull) {
      return next(new ErrorResponse('Access denied', 403));
    }

    const previewUrl = content.previewUrl;
    if (!previewUrl) {
      return next(new ErrorResponse('Preview not available', 404));
    }

    // Set security headers
    res.setHeader('Cache-Control', 'private, no-cache');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');

    if (isUsingS3Storage() && previewUrl.includes('s3')) {
      // Stream from S3
      const s3Key = extractS3Key(previewUrl);
      if (!s3Key) {
        return next(new ErrorResponse('Invalid preview URL', 400));
      }
      await streamFromS3(s3Key, req, res);
    } else {
      // Stream local file
      streamLocalFile(previewUrl, req, res);
    }

  } catch (error) {
    console.error('Error serving preview:', error);
    next(new ErrorResponse('Error serving preview', 500));
  }
};

/**
 * @desc    Stream content with range support (optimized for video)
 * @route   GET /api/proxy/stream/:contentId
 * @access  Private (requires authentication and purchase)
 */
exports.streamContent = async (req, res, next) => {
  try {
    const { contentId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Get content
    const content = await Content.findById(contentId);
    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    // Check access permissions - allow both full and preview access for streaming
    const fileAccess = await getContentFileAccess(content, userId, userRole);
    if (!fileAccess.canAccessFull && !fileAccess.canAccessPreview) {
      return next(new ErrorResponse('Access denied. Authentication required to stream content.', 403));
    }

    // For preview access, use preview URL if available, otherwise use full URL
    let streamFileUrl = content.fileUrl;
    if (!fileAccess.canAccessFull && fileAccess.canAccessPreview && content.previewUrl) {
      streamFileUrl = content.previewUrl;
    }

    if (!streamFileUrl) {
      return next(new ErrorResponse('Content file not available', 404));
    }

    // Set streaming headers
    res.setHeader('Cache-Control', 'private, no-cache');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');

    if (isUsingS3Storage() && streamFileUrl.includes('s3')) {
      // Stream from S3
      const s3Key = extractS3Key(streamFileUrl);
      if (!s3Key) {
        return next(new ErrorResponse('Invalid file URL', 400));
      }
      await streamFromS3(s3Key, req, res);
    } else {
      // Stream local file
      streamLocalFile(streamFileUrl, req, res);
    }

  } catch (error) {
    console.error('Error streaming content:', error);
    next(new ErrorResponse('Error streaming content', 500));
  }
};

/**
 * @desc    Serve thumbnail through proxy
 * @route   GET /api/proxy/thumbnail/:contentId
 * @access  Private (requires authentication)
 */
exports.serveThumbnail = async (req, res, next) => {
  try {
    const { contentId } = req.params;

    // Get content
    const content = await Content.findById(contentId);
    if (!content) {
      return next(new ErrorResponse('Content not found', 404));
    }

    const thumbnailUrl = content.thumbnailUrl;
    if (!thumbnailUrl) {
      return next(new ErrorResponse('Thumbnail not available', 404));
    }

    // Set caching headers for thumbnails (they can be cached longer)
    res.setHeader('Cache-Control', 'private, max-age=3600');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.setHeader('Access-Control-Allow-Headers', 'Range, Content-Type, Authorization');

    if (isUsingS3Storage() && thumbnailUrl.includes('s3')) {
      // Stream from S3
      const s3Key = extractS3Key(thumbnailUrl);
      if (!s3Key) {
        return next(new ErrorResponse('Invalid thumbnail URL', 400));
      }
      await streamFromS3(s3Key, req, res);
    } else {
      // Stream local file
      streamLocalFile(thumbnailUrl, req, res);
    }

  } catch (error) {
    console.error('Error serving thumbnail:', error);
    next(new ErrorResponse('Error serving thumbnail', 500));
  }
};
